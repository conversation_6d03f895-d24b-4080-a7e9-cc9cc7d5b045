import json
import os
import sys
import pandas as pd
import numpy as np
import re
import logging
import time
import traceback
from datetime import datetime
from openpyxl.styles import PatternFill

# Configure logging
def setup_logging():
    """Set up logging to both console and file."""
    # Create logs directory if it doesn't exist
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
    os.makedirs(logs_dir, exist_ok=True)

    # Use a single log file named "log"
    log_file = os.path.join(logs_dir, "log")

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Clear any existing handlers to prevent duplicate logs
    if logger.handlers:
        logger.handlers.clear()

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Create file handler with append mode
    file_handler = logging.FileHandler(log_file, mode='a')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Set up logging
logger = setup_logging()

def process_json_and_generate_report(timestamped_dir: str) -> str:
    """
    Process JSON files and generate Excel report.

    Args:
        timestamped_dir: Path to the directory containing the response folder

    Returns:
        Path to the generated Excel file
    """
    logger.info(f"Starting Excel report generation for: {timestamped_dir}")
    start_time = time.time()

    response_folder = os.path.join(timestamped_dir, "response")
    output_folder = os.path.join(timestamped_dir, "output")

    logger.info(f"Response folder: {response_folder}")
    logger.info(f"Output folder: {output_folder}")

    # Check if response folder exists, raise exception if not
    if not os.path.exists(response_folder):
        raise FileNotFoundError(f"The 'response' folder does not exist at {response_folder}")

    # Create the output folder if it does not exist
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # Define the file path for the output Excel file
    output_file = os.path.join(output_folder, "transactions.xlsx")

    # Function to clean and convert amount to float
    def clean_amount(amount):
        if not amount or pd.isna(amount):  # Handle None, empty, or NA values
            return pd.NA

        # Convert to string to ensure we can process it
        amount_str = str(amount)

        # Remove commas
        amount_str = amount_str.replace(',', '')

        # Remove all special characters except for the decimal point and minus sign
        # This regex keeps only digits, decimal points, and minus signs
        amount_str = re.sub(r'[^\d.\-]', '', amount_str)

        try:
            # Convert to float
            amount_float = float(amount_str)
            # Make negative amounts positive
            return abs(amount_float)
        except (ValueError, TypeError):
            return pd.NA

    # Function to clean description text by replacing <br> tags with a space
    def clean_description(description):
        if not description or pd.isna(description):  # Handle None, empty, or NA values
            return pd.NA

        # Convert to string to ensure we can process it
        description_str = str(description)

        # Replace <br> tags (case insensitive) with a single space
        description_str = re.sub(r'<br\s*/?>', ' ', description_str, flags=re.IGNORECASE)

        return description_str

    # Function to transform date by appending year from start_date
    def transform_date(date_str, start_year):
        if not date_str or pd.isna(date_str):  # Handle None, empty, or NA values
            return pd.NA
        try:
            # Clean the date string
            date_str = str(date_str).strip()
            # logger.info(f"Transforming date: '{date_str}' with start_year: '{start_year}'")

            # Check if the date already has a year component
            if len(date_str.split('/')) >= 3:
                # Date already has year, just parse it
                logger.info(f"Date '{date_str}' already has year component")
                result = pd.to_datetime(date_str, format="%m/%d/%Y", errors='coerce')
                logger.info(f"Parsed date with existing year: {result}")
                # Return only the date part without time
                return result.date() if result is not pd.NaT else pd.NA

            # Make sure start_year is a 4-digit year
            if start_year and len(start_year) == 2:
                # Convert 2-digit year to 4-digit year
                century = "20" if int(start_year) < 50 else "19"  # Assume 00-49 is 2000s, 50-99 is 1900s
                full_year = f"{century}{start_year}"
                # logger.info(f"Converted 2-digit year '{start_year}' to 4-digit year '{full_year}'")
            elif start_year and len(start_year) == 4:
                full_year = start_year
                logger.info(f"Using provided 4-digit year: {full_year}")
            else:
                # If we can't determine the year, use current year
                full_year = str(datetime.now().year)
                logger.info(f"Using current year as fallback: {full_year}")

            # Append the year to the date string
            full_date = f"{date_str}/{full_year}"
            # logger.info(f"Full date string: '{full_date}'")

            # Try to parse with different formats
            for fmt in ["%m/%d/%Y", "%d/%m/%Y"]:
                try:
                    result = pd.to_datetime(full_date, format=fmt)
                    # logger.info(f"Successfully parsed date with format '{fmt}': {result}")
                    # Return only the date part without time
                    return result.date() if result is not pd.NaT else pd.NA
                except ValueError:
                    logger.info(f"Failed to parse date with format '{fmt}'")
                    continue

            # If all formats fail, try a more flexible approach
            logger.info(f"Trying flexible date parsing for: '{full_date}'")
            result = pd.to_datetime(full_date, errors='coerce')
            logger.info(f"Flexible parsing result: {result}")
            # Return only the date part without time
            return result.date() if result is not pd.NaT else pd.NA

        except (ValueError, TypeError) as e:
            logger.error(f"Error parsing date '{date_str}': {e}")
            return pd.NA

    # Step 1: Look for a JSON file containing "OpeningPage" in its name
    start_year = None
    previous_balance = 0.0  # Default to 0.0 if not found
    start_date = None
    extracted_credit_count = 0
    extracted_debit_count = 0
    for filename in os.listdir(response_folder):
        if "openingpage" in filename.lower() and filename.endswith(".json"):
            file_path = os.path.join(response_folder, filename)
            with open(file_path, 'r') as file:
                data = json.load(file)
                previous_balance_raw = data.get("Previous Balance")
                current_balance_raw = data.get("Current Balance")
                start_date = data.get("Statement Period", {}).get("Start Date")
                end_date = data.get("Statement Period", {}).get("End Date")

                # Extract transaction counts
                deposits_credits = data.get("Deposits/Credits", {})
                checks_debits = data.get("Checks/Debits", {})
                extracted_credit_count = deposits_credits.get("Count", 0) if isinstance(deposits_credits, dict) else 0
                extracted_debit_count = checks_debits.get("Count", 0) if isinstance(checks_debits, dict) else 0

                extracted_credit_amount = deposits_credits.get("Total Amount", 0) if isinstance(deposits_credits, dict) else 0
                extracted_debit_amount = checks_debits.get("Total Amount", 0) if isinstance(checks_debits, dict) else 0

                service_charge = data.get("Service Charge")

                logger.info(f"Extracted transaction counts - Credits: {extracted_credit_count}, Debits: {extracted_debit_count}")

                if previous_balance_raw:
                    previous_balance_cleaned = clean_amount(previous_balance_raw)
                    if not pd.isna(previous_balance_cleaned):
                        previous_balance = previous_balance_cleaned
                if current_balance_raw:
                    current_balance_cleaned = clean_amount(current_balance_raw)
                    if not pd.isna(current_balance_cleaned):
                        current_balance = current_balance_cleaned
                if start_date:
                    try:
                        start_year = start_date.split('/')[-1]
                    except (ValueError, TypeError):
                        start_year = None
                else:
                    # If start_date is empty, try to infer year from the data
                    # Default to current year - 1 for December dates (common for year-end statements)
                    start_year = "24"  # Assuming 2024 for December dates
                    logger.info(f"Start date is empty, defaulting to year: 20{start_year}")
                break
    else:
        print("No JSON file containing 'OpeningPage' in its name found in the folder.")
        return

    # Step 2: Initialize an empty DataFrame for credits, debits, and checks
    df = pd.DataFrame(columns=["Date", "Description", "Credit", "Debit"])

    # Step 3: Look for a JSON file containing "CreditPages" in its name
    for filename in os.listdir(response_folder):
        if "creditpages" in filename.lower() and filename.endswith(".json"):
            file_path = os.path.join(response_folder, filename)
            with open(file_path, 'r') as file:
                data = json.load(file)
                credits = data.get("CreditsInfo", [])
                credit_df = pd.DataFrame(credits, columns=["Date", "Description", "Amount"])
                credit_df["Amount"] = credit_df["Amount"].apply(clean_amount)
                credit_df["Description"] = credit_df["Description"].apply(clean_description)
                credit_df = credit_df.rename(columns={"Amount": "Credit"})
                credit_df["Debit"] = pd.NA
                if start_year:
                    credit_df["Date"] = credit_df["Date"].apply(lambda x: transform_date(x, start_year))
                if not credit_df.empty:
                    df = pd.concat([df, credit_df], ignore_index=True)
                break
    else:
        print("No JSON file containing 'CreditPages' in its name found in the folder.")

    # Step 4: Look for a JSON file containing "DebitPages" in its name
    for filename in os.listdir(response_folder):
        if "debitpages" in filename.lower() and filename.endswith(".json"):
            file_path = os.path.join(response_folder, filename)
            with open(file_path, 'r') as file:
                data = json.load(file)
                debits = data.get("DebitInfo", [])
                debit_df = pd.DataFrame(debits, columns=["Date", "Description", "Amount"])
                debit_df["Amount"] = debit_df["Amount"].apply(clean_amount)
                debit_df["Description"] = debit_df["Description"].apply(clean_description)
                debit_df = debit_df.rename(columns={"Amount": "Debit"})
                debit_df["Credit"] = pd.NA
                if start_year:
                    debit_df["Date"] = debit_df["Date"].apply(lambda x: transform_date(x, start_year))
                if not debit_df.empty:
                    df = pd.concat([df, debit_df], ignore_index=True)
                break
    else:
        print("No JSON file containing 'DebitPages' in its name found in the folder.")

    # Step 5: Look for a JSON file containing "CheckPages" in its name
    for filename in os.listdir(response_folder):
        if "checkpages" in filename.lower() and filename.endswith(".json"):
            file_path = os.path.join(response_folder, filename)
            with open(file_path, 'r') as file:
                data = json.load(file)
                checks = data.get("CheckNumberinOrder", [])
                check_df = pd.DataFrame(checks, columns=["Date", "CheckNo", "Amount"])
                check_df["Amount"] = check_df["Amount"].apply(clean_amount)
                check_df = check_df.rename(columns={"Date": "Date", "CheckNo": "Description", "Amount": "Debit"})
                check_df["Description"] = check_df["Description"].apply(clean_description)
                check_df["Credit"] = pd.NA
                if start_year:
                    check_df["Date"] = check_df["Date"].apply(lambda x: transform_date(x, start_year))
                if not check_df.empty:
                    df = pd.concat([df, check_df], ignore_index=True)
                break
    else:
        print("No JSON file containing 'CheckPages' in its name found in the folder.")

    # Step 6: Convert Credit and Debit columns to float type
    df["Credit"] = df["Credit"].astype("float64", errors="ignore")
    df["Debit"] = df["Debit"].astype("float64", errors="ignore")

    # Step 7: Add the Amount column
    df["Amount"] = pd.NA
    df.loc[df["Debit"].notna(), "Amount"] = df["Debit"]
    df.loc[df["Credit"].notna(), "Amount"] = -df["Credit"]
    df["Amount"] = df["Amount"].astype("float64", errors="ignore")

    # Step 8: Remove rows that have a value in only one column
    # Count non-NA values in each row (excluding Date which is always present)
    non_na_count = df.iloc[:, 1:].notna().sum(axis=1)  # Count non-NA values starting from Description column
    # Keep rows that have at least 2 non-NA values (more than just one column with data)
    df = df[non_na_count >= 2].reset_index(drop=True)

    # Step 9: Sort the DataFrame by Date
    df = df.sort_values(by="Date").reset_index(drop=True)

    # Step 9: Add the Balance column (will be populated with Excel formulas later)
    df["Balance"] = pd.NA

    # Step 10: Reorder columns to match desired layout: Date, Description, Debit, Credit, Amount, Balance
    df = df[["Date", "Description", "Debit", "Credit", "Amount", "Balance"]]

    def categorize(row):
        desc = row["Description"].upper() if pd.notna(row["Description"]) else ""

        # Priority conditions based on Description
        if "IRS" in desc:
            return "Payroll Tax", "Payroll Tax"
        elif "DATA LINKBNKCARD" in desc or "DATALINKBNKCARD" in desc:
            return "Data Link", "Telephone & Internet"
        elif "MTOT DISC BANKCARD-1063" in desc:
            return "Merchant service charge", "Merchant service charge"
        elif "MO REV TAX JP MO REV TAX" in desc:
            return "Sales Tax", "Sales Tax"
        elif "TRANSFER TO LOAN ACCT NO. ACCT ENDING 3664" in desc:
            return "LOAN-63664", "LOAN-63664"
        elif "CHARGEBACK" in desc:
            return "BANK CHARGES", "BANK CHARGES"
        elif "ORGILL" in desc:
            return "ORGILL INC", "INVENTORY"
        elif "SERVICE CHARGE" in desc:
            return "BANK CHARGES", "BANK CHARGES"

        # Default condition based on Credit
        elif pd.notna(row["Credit"]) and row["Credit"] != 0:
            return "Deposit", "Income"

        return "", ""  # Default empty if no condition matches

    # Apply the function to create the new columns
    df[["Category", "Type"]] = df.apply(categorize, axis=1, result_type="expand")

    # Step 10.5: Format currency columns for display (this helps with Excel display)
    def format_currency(value):
        """Format numeric values as currency strings for better Excel display"""
        if pd.isna(value) or value is None:
            return None
        try:
            return float(value)  # Keep as float for Excel formatting
        except (ValueError, TypeError):
            return None

    # Apply formatting to currency columns
    for col in ["Debit", "Credit", "Amount", "Balance"]:
        df[col] = df[col].apply(format_currency)

    # Step 11: Save the DataFrame to Excel with the updated layout
    heading_part1 = f"Starting Balance as on {start_date if start_date else 'Unknown'}:"
    heading_part2 = previous_balance

    heading_part3 = f"Ending balance as on {end_date if end_date else 'Unknown'}:"
    heading_part4 = current_balance

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Sheet1', startrow=4, index=False)
        workbook = writer.book
        worksheet = writer.sheets['Sheet1']

        worksheet['A1'] = heading_part1
        worksheet['A2'] = heading_part2

        # Adding current balance
        worksheet['A3'] = heading_part3
        worksheet['A4'] = heading_part4

        # adding red colour to all four heading if wrong and green if correct
        # Define fill colors for red and green
        red_fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")
        green_fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")

        if round(previous_balance + (extracted_credit_amount - extracted_debit_amount - service_charge), 2) != round(current_balance, 2):
            worksheet['A2'].fill = red_fill
            worksheet['A4'].fill = red_fill
        else:
            worksheet['A2'].fill = green_fill
            worksheet['A4'].fill = green_fill

        # # Load extracted daily ending balances for comparison
        extracted_daily_balances = {}
        # for filename in os.listdir(response_folder):
        #     if ("endingbalance" in filename.lower() or "dailyendingbalance" in filename.lower()) and filename.endswith(".json"):
        #         file_path = os.path.join(response_folder, filename)
        #         try:
        #             with open(file_path, 'r') as file:
        #                 balance_data = json.load(file)
        #                 daily_balances = balance_data.get("dailyEndingBalance", [])
        #                 for balance_entry in daily_balances:
        #                     date_str = balance_entry.get("Date", "")
        #                     balance_val = balance_entry.get("Balance", 0)
        #                     if date_str and balance_val is not None:
        #                         # Normalize date format for comparison
        #                         try:
        #                             # Handle different date formats
        #                             if "/" in date_str:
        #                                 parts = date_str.split("/")
        #                                 if len(parts) >= 2:
        #                                     month = int(parts[0])
        #                                     day = int(parts[1])
        #                                     # Use the same year as start_year, but handle 2-digit years
        #                                     if start_year and len(start_year) == 2:
        #                                         # Convert 2-digit year to 4-digit year
        #                                         century = "20" if int(start_year) < 50 else "19"
        #                                         year = int(f"{century}{start_year}")
        #                                     elif start_year and len(start_year) == 4:
        #                                         year = int(start_year)
        #                                     else:
        #                                         year = datetime.now().year
        #                                     normalized_date = datetime(year, month, day).date()
        #                                     extracted_daily_balances[normalized_date] = float(balance_val)
        #                                     logger.info(f"Loaded extracted balance for {normalized_date}: ${balance_val}")
        #                         except Exception as e:
        #                             logger.warning(f"Could not parse date '{date_str}': {e}")
        #                 logger.info(f"Loaded {len(extracted_daily_balances)} extracted daily balances")
        #                 break
        #         except Exception as e:
        #             logger.warning(f"Could not load daily ending balances from {filename}: {e}")

        # Load true daily ending balances from EndingBalance_trueData.json
        true_daily_balance_df = None
        true_data_path = os.path.join(response_folder, "EndingBalance_trueData.json")
        if os.path.exists(true_data_path):
            try:
                with open(true_data_path, 'r') as file:
                    true_data = json.load(file)
                    true_daily_balances = true_data.get("dailyEndingBalance", [])
                    # Build DataFrame
                    true_daily_balance_df = pd.DataFrame(true_daily_balances)
                    # Rename Balance to Amount for consistency
                    true_daily_balance_df = true_daily_balance_df.rename(columns={"Balance": "Amount"})
                    # Convert Date to datetime.date using the same logic as transform_date
                    def parse_true_date(date_str):
                        if not date_str or pd.isna(date_str):
                            return pd.NA
                        try:
                            parts = str(date_str).split("/")
                            if len(parts) >= 2:
                                month = int(parts[0])
                                day = int(parts[1])
                                if start_year and len(start_year) == 2:
                                    century = "20" if int(start_year) < 50 else "19"
                                    year = int(f"{century}{start_year}")
                                elif start_year and len(start_year) == 4:
                                    year = int(start_year)
                                else:
                                    year = datetime.now().year
                                return datetime(year, month, day).date()
                        except Exception as e:
                            logger.warning(f"Could not parse trueData date '{date_str}': {e}")
                            return pd.NA
                    true_daily_balance_df["Date"] = true_daily_balance_df["Date"].apply(parse_true_date)
            except Exception as e:
                logger.warning(f"Could not load or parse EndingBalance_trueData.json: {e}")
        else:
            logger.info("EndingBalance_trueData.json not found in response folder.")

        # Calculate difference between consecutive dates in true_daily_balance_df
        if true_daily_balance_df is not None and not true_daily_balance_df.empty:
            true_daily_balance_df = true_daily_balance_df.sort_values(by="Date").reset_index(drop=True)
            true_daily_balance_df["Delta"] = true_daily_balance_df["Amount"].diff()
            logger.info(f"Added Delta column to true_daily_balance_df. Example:\n{true_daily_balance_df.head()}")

        # Calculate actual daily ending balances from transaction data
        calculated_daily_balances = {}
        running_balance = previous_balance

        # Group transactions by date and calculate daily ending balances
        df_sorted = df.sort_values(by="Date").reset_index(drop=True)
        current_date = None
        daily_credits = 0.0
        daily_debits = 0.0

        for _, row in df_sorted.iterrows():
            transaction_date = row["Date"]
            if pd.isna(transaction_date):
                continue

            # Convert to date object if it's not already
            if isinstance(transaction_date, str):
                try:
                    transaction_date = pd.to_datetime(transaction_date).date()
                except:
                    continue
            elif hasattr(transaction_date, 'date'):
                transaction_date = transaction_date.date()

            # If we're on a new date, calculate the ending balance for the previous date
            if current_date is not None and transaction_date != current_date:
                ending_balance = running_balance + daily_credits - daily_debits
                calculated_daily_balances[current_date] = ending_balance
                running_balance = ending_balance
                logger.info(f"Calculated ending balance for {current_date}: ${ending_balance:.2f} (Credits: ${daily_credits:.2f}, Debits: ${daily_debits:.2f})")
                daily_credits = 0.0
                daily_debits = 0.0

            current_date = transaction_date

            # Add current transaction to daily totals
            credit_amount = row["Credit"] if pd.notna(row["Credit"]) else 0.0
            debit_amount = row["Debit"] if pd.notna(row["Debit"]) else 0.0
            daily_credits += credit_amount
            daily_debits += debit_amount

        # Don't forget the last date
        if current_date is not None:
            ending_balance = running_balance + daily_credits - daily_debits
            calculated_daily_balances[current_date] = ending_balance
            logger.info(f"Calculated ending balance for {current_date}: ${ending_balance:.2f} (Credits: ${daily_credits:.2f}, Debits: ${daily_debits:.2f})")

        # Calculate actual transaction counts from the data
        actual_credit_count = df["Credit"].notna().sum()  # Count non-null credit transactions
        actual_debit_count = df["Debit"].notna().sum()    # Count non-null debit transactions

        # Calculate actual transaction totals from the data
        actual_credit_total = df["Credit"].sum()  # Sum of all credit amounts (NaN values are ignored)
        actual_debit_total = df["Debit"].sum()    # Sum of all debit amounts (NaN values are ignored)

        # Handle NaN values - if all values are NaN, sum() returns 0.0, but we want to ensure it's a valid number
        actual_credit_total = actual_credit_total if not pd.isna(actual_credit_total) else 0.0
        actual_debit_total = actual_debit_total if not pd.isna(actual_debit_total) else 0.0

        logger.info(f"Calculated transaction counts - Credits: {actual_credit_count}, Debits: {actual_debit_count}")
        logger.info(f"Calculated transaction totals - Credits: ${actual_credit_total:.2f}, Debits: ${actual_debit_total:.2f}")

        # Store the balance data for later use in color coding
        balance_comparison_data = {
            'extracted_daily_balances': extracted_daily_balances,
            'calculated_daily_balances': calculated_daily_balances,
            'df_sorted': df_sorted
        }

        # Updated column positions: C=Debit, D=Credit
        worksheet['C1'] = "Total Debit Transactions"
        worksheet['D1'] = "Total Credit Transactions"
        worksheet['C2'] = "=SUBTOTAL(3,C6:C1000)"
        worksheet['D2'] = "=SUBTOTAL(3,D6:D1000)"

        # Apply color coding to transaction count cells
        # Compare extracted counts with calculated counts
        if (extracted_debit_count + 1) == (actual_debit_count):
            worksheet['C2'].fill = green_fill
            logger.info(f"MATCH Debit Count: Extracted {extracted_debit_count} matches calculated {actual_debit_count}")
        else:
            worksheet['C2'].fill = red_fill
            logger.warning(f"MISMATCH Debit Count: Extracted {extracted_debit_count} vs calculated {actual_debit_count}")

        if extracted_credit_count == actual_credit_count:
            worksheet['D2'].fill = green_fill
            logger.info(f"MATCH Credit Count: Extracted {extracted_credit_count} matches calculated {actual_credit_count}")
        else:
            worksheet['D2'].fill = red_fill
            logger.warning(f"MISMATCH Credit Count: Extracted {extracted_credit_count} vs calculated {actual_credit_count}")

        worksheet['C3'] = "Total Debit Amount"
        worksheet['D3'] = "Total Credit Amount"
        worksheet['C4'] = "=SUBTOTAL(9,C6:C1000)"
        worksheet['D4'] = "=SUBTOTAL(9,D6:D1000)"

        # Apply color coding to transaction total amount cells

        if ((extracted_debit_amount + service_charge)) == actual_debit_total:
            worksheet['C4'].fill = green_fill
            logger.info(f"MATCH Debit Total: Extracted ${(extracted_debit_amount + service_charge):.2f} matches calculated ${actual_debit_total:.2f}")
        else:
            worksheet['C4'].fill = red_fill
            logger.warning(f"MISMATCH Debit Total: Extracted ${(extracted_debit_amount + service_charge):.2f} vs calculated ${actual_debit_total:.2f}")

        if (extracted_credit_amount) == actual_credit_total:
            worksheet['D4'].fill = green_fill
            logger.info(f"MATCH Credit Total: Extracted ${extracted_credit_amount:.2f} matches calculated ${actual_credit_total:.2f}")
        else:
            worksheet['D4'].fill = red_fill
            logger.warning(f"MISMATCH Credit Total: Extracted ${extracted_credit_amount:.2f} vs calculated ${actual_credit_total:.2f}")

        worksheet.auto_filter.ref = "A5:H5"

        # Apply USD currency formatting to Debit, Credit, Amount, and Balance columns
        from openpyxl.styles import NamedStyle

        # Create USD currency style
        currency_style = NamedStyle(name="currency_usd")
        currency_style.number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # Apply currency formatting to data rows (starting from row 6, which is startrow=4 + header row + 1)
        # Get the last row with data
        last_row = len(df) + 5  # startrow=4 + header row + data rows

        # Format Date column (A) to show only date without time
        for row in range(6, last_row + 1):
            worksheet[f'A{row}'].number_format = 'mm/dd/yyyy'

        # Format Debit column (C)
        for row in range(6, last_row + 1):
            worksheet[f'C{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # Format Credit column (D)
        for row in range(6, last_row + 1):
            worksheet[f'D{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # Format Amount column (E)
        for row in range(6, last_row + 1):
            worksheet[f'E{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # Format Balance column (F)
        for row in range(6, last_row + 1):
            worksheet[f'F{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # Format the summary cells as well
        worksheet['A2'].number_format = '"$"#,##0.00_);("$"#,##0.00)'
        worksheet['C2'].number_format = '#,##0'  # Count format for transaction count
        worksheet['D2'].number_format = '#,##0'  # Count format for transaction count
        worksheet['C4'].number_format = '"$"#,##0.00_);("$"#,##0.00)'  # Total amounts
        worksheet['D4'].number_format = '"$"#,##0.00_);("$"#,##0.00)'  # Total amounts

        # Add Excel formulas for Balance column (Column F)
        # The balance calculation: Previous Balance + Credit - Debit
        logger.info(f"Adding Excel formulas for Balance column with previous_balance: {previous_balance}")

        for row in range(6, last_row + 1):  # Starting from row 6 (first data row)
            if row == 6:  # First row: Previous Balance + Credit - Debit
                worksheet[f'F{row}'] = f'=+$A$2+D{row}-C{row}'
            else:  # Subsequent rows: Previous Balance + Credit - Debit
                prev_row = row - 1
                worksheet[f'F{row}'] = f'=+F{prev_row}+D{row}-C{row}'

            # Apply currency formatting to the formula cell
            worksheet[f'F{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # # Apply color coding to rows based on daily balance comparison
        # tolerance = 0.01  # Allow for small rounding differences

        # for row in range(6, last_row + 1):  # Starting from row 6 (first data row)
        #     try:
        #         # Get the date for this row
        #         row_date = balance_comparison_data['df_sorted'].iloc[row - 6]["Date"]  # Adjust for 0-based indexing
        #         if pd.isna(row_date):
        #             continue

        #         # Convert to date object if needed
        #         if isinstance(row_date, str):
        #             try:
        #                 row_date = pd.to_datetime(row_date).date()
        #             except:
        #                 continue
        #         elif hasattr(row_date, 'date'):
        #             row_date = row_date.date()

        #         # Check if we have both extracted and calculated balances for this date
        #         extracted_balance = balance_comparison_data['extracted_daily_balances'].get(row_date)
        #         calculated_balance = balance_comparison_data['calculated_daily_balances'].get(row_date)

        #         if extracted_balance is not None and calculated_balance is not None:
        #             # Compare balances within tolerance
        #             balance_difference = abs(extracted_balance - calculated_balance)
        #             if balance_difference <= tolerance:
        #                 # Balances match - color row green
        #                 for col in ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']:
        #                     worksheet[f'{col}{row}'].fill = green_fill
        #                 logger.info(f"MATCH Date {row_date}: Extracted ${extracted_balance:.2f} matches calculated ${calculated_balance:.2f}")
        #             else:
        #                 # Balances don't match - color row red
        #                 for col in ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']:
        #                     worksheet[f'{col}{row}'].fill = red_fill
        #                 logger.warning(f"MISMATCH Date {row_date}: Extracted ${extracted_balance:.2f} vs calculated ${calculated_balance:.2f} (diff: ${balance_difference:.2f})")

        #     except Exception as e:
        #         logger.warning(f"Error applying color coding to row {row}: {e}")

        # logger.info(f"Applied color coding to {last_row - 5} transaction rows")
        
    # Create a DataFrame with the sum of Amount for each date from df (no absolute value)
    if not df.empty:
        # Group by Date and sum the Amount for each date
        ending_balance_df = df.groupby("Date", as_index=False)["Amount"].sum().reset_index()
        ending_balance_df = ending_balance_df.rename(columns={"Amount": "endingBalance"})
        # Calculate delta between consecutive dates
        ending_balance_df = ending_balance_df.sort_values(by="Date").reset_index(drop=True)
        ending_balance_df["Delta"] = ending_balance_df["endingBalance"].diff()
        logger.info(f"Created ending_balance_df with sum of Amount per date and Delta. Example:\n{ending_balance_df.head()}")

    # --- Color coding based on absolute ending balance match with true data ---
    # Only proceed if both DataFrames exist and are not empty
    if (
        'ending_balance_df' in locals() and ending_balance_df is not None and not ending_balance_df.empty and
        'true_daily_balance_df' in locals() and true_daily_balance_df is not None and not true_daily_balance_df.empty
    ):
        # Merge on Date to align both DataFrames
        merged = pd.merge(
            ending_balance_df[["Date", "endingBalance"]],
            true_daily_balance_df[["Date", "Amount"]],
            on="Date",
            how="inner"
        )
        # Tolerance for floating point comparison
        tolerance = 0.01
        # Build a set of dates where abs(endingBalance) matches abs(Amount)
        match_dates = set()
        mismatch_dates = set()
        for _, row in merged.iterrows():
            if pd.notna(row["endingBalance"]) and pd.notna(row["Amount"]):
                if abs(abs(row["endingBalance"]) - abs(row["Amount"])) <= tolerance:
                    match_dates.add(row["Date"])
                else:
                    mismatch_dates.add(row["Date"])
        # Color C,D,E,F for each row in the Excel sheet based on match
        for row_idx in range(6, last_row + 1):
            excel_date = worksheet[f'A{row_idx}'].value
            # Convert to date if needed
            if isinstance(excel_date, str):
                try:
                    excel_date = pd.to_datetime(excel_date).date()
                except Exception as e:
                    logger.warning(f"Could not parse date in Excel row {row_idx}: {e}")
                    continue
            elif hasattr(excel_date, 'date'):
                excel_date = excel_date.date()
            if excel_date in match_dates:
                fill = green_fill
                logger.info(f"Row {row_idx} ({excel_date}): GREEN")
            elif excel_date in mismatch_dates:
                fill = red_fill
                logger.info(f"Row {row_idx} ({excel_date}): RED")
            else:
                continue  # No comparison available for this date
            for col in ['C', 'D', 'E', 'F']:
                cell = worksheet[f'{col}{row_idx}']
                cell.fill = fill
        logger.info(f"Applied color coding to C,D,E,F columns based on absolute ending balance match with true data.")
    else:
        logger.info("Skipping color coding based on ending balance match. Required DataFrames are not available.")
    logger.info(f"DataFrame has been saved to {output_file}")

    # Calculate and log processing time
    end_time = time.time()
    processing_time = end_time - start_time
    logger.info(f"Excel report generation completed in {processing_time:.2f} seconds")

    # Print a message to the console as well
    print(f"\n✅ Excel report has been saved to {output_file}")
    print(f"Processing time: {processing_time:.2f} seconds")

    return output_file

if __name__ == "__main__":
    try:
        # Call the function with the folder path (timestamped_dir)
        print("Excel Report Generator")
        print("=====================")
        timestamped_dir = r"Data1\20250625_120132_93fff5fe"

        if not os.path.exists(timestamped_dir):
            print(f"❌ Error: Directory does not exist: {timestamped_dir}")
            sys.exit(1)

        output_file = process_json_and_generate_report(timestamped_dir)
        print(f"\n✅ Success! Excel report generated at: {output_file}")

    except Exception as e:
        print(f"\n❌ Error: {e}")
        traceback.print_exc()
        sys.exit(1)